# Dashboard Migration: From Assessment Results to Jobs Data

## Overview
File `useDashboard.js` telah diperbarui untuk menggunakan data jobs dari endpoint `/api/archive/jobs` sebagai pengganti data assessment results. Perubahan ini memungkinkan dashboard menampilkan data jobs yang dicari oleh user dengan informasi archetype yang diambil dari endpoint `/api/archive/results/:id`.

## Changes Made

### 1. API Configuration (`src/config/api.js`)
- **Added**: `JOBS: '/api/archive/jobs'` endpoint ke `API_ENDPOINTS.ARCHIVE`

### 2. API Service (`src/services/apiService.js`)
- **Added**: `getJobs(params)` method untuk mengambil data jobs dengan parameter:
  - `page`: Halaman data (default: 1)
  - `limit`: Jumlah data per halaman (default: 10)
  - `status`: Filter berdasarkan status ('pending', 'processing', 'completed', 'failed')
  - `assessment_name`: Filter berdasarkan nama assessment
  - `sort`: Field untuk sorting (default: 'created_at')
  - `order`: Urutan sorting (default: 'DESC')

### 3. Dashboard Hook (`src/hooks/useDashboard.js`)

#### Data Structure Changes:
- **Changed**: `data.results` → `data.jobs` (internal)
- **Changed**: `loading.results` → `loading.jobs` (internal)
- **Changed**: `error.results` → `error.jobs` (internal)

#### New Functionality:
- **Enhanced**: `fetchJobs()` function yang:
  1. Mengambil data jobs dari `/api/archive/jobs`
  2. Untuk setiap job dengan status 'completed' dan memiliki `result_id`, mengambil archetype dari `/api/archive/results/:id`
  3. Menambahkan field `id`, `archetype`, dan `persona_profile` ke setiap job

#### Backward Compatibility:
- **Added**: Alias `results: data.jobs` untuk kompatibilitas dengan komponen existing
- **Added**: Alias `results: loading.jobs` untuk loading state
- **Added**: Alias `results: error.jobs` untuk error state
- **Added**: Alias `fetchResults: fetchJobs` untuk action compatibility
- **Added**: Alias `deleteResult: deleteJob` untuk action compatibility

## Data Transformation

### Input (Jobs API Response):
```json
{
  "success": true,
  "data": {
    "jobs": [
      {
        "job_id": "job_12345abcdef",
        "user_id": "550e8400-e29b-41d4-a716-446655440001",
        "status": "completed",
        "assessment_name": "AI-Driven Talent Mapping",
        "created_at": "2024-01-15T10:30:00.000Z",
        "updated_at": "2024-01-15T10:32:00.000Z",
        "result_id": "550e8400-e29b-41d4-a716-446655440003"
      }
    ],
    "total": 25
  }
}
```

### Output (Transformed for Dashboard):
```json
{
  "job_id": "job_12345abcdef",
  "user_id": "550e8400-e29b-41d4-a716-446655440001",
  "status": "completed",
  "assessment_name": "AI-Driven Talent Mapping",
  "created_at": "2024-01-15T10:30:00.000Z",
  "updated_at": "2024-01-15T10:32:00.000Z",
  "result_id": "550e8400-e29b-41d4-a716-446655440003",
  "id": "550e8400-e29b-41d4-a716-446655440003",
  "archetype": "The Innovator",
  "persona_profile": {
    "archetype": "The Innovator",
    // ... other persona profile data
  }
}
```

## Component Compatibility

### Existing Components (No Changes Required):
- `Dashboard.jsx` - Tetap menggunakan `data.results` (alias)
- `ResultsTable.jsx` - Tetap menggunakan `data.results` (alias)
- `StatsCards.jsx` - Tetap menggunakan struktur data yang sama

### Key Fields Mapping:
- `result.id` → `job.result_id || job.job_id` (untuk completed jobs, menggunakan result_id; untuk lainnya menggunakan job_id)
- `result.assessment_name` → `job.assessment_name`
- `result.status` → `job.status`
- `result.created_at` → `job.created_at`
- `result.persona_profile.archetype` → Diambil dari `/api/archive/results/:id` untuk completed jobs

## Testing

### Test File: `src/hooks/useDashboard.test.js`
- Tests initialization with correct default state
- Tests jobs fetching and data transformation
- Tests API error handling
- Tests backward compatibility aliases

### Run Tests:
```bash
npm test src/hooks/useDashboard.test.js
```

## Usage Example

```javascript
import { useDashboard } from '../hooks/useDashboard';

function DashboardComponent() {
  const { data, loading, actions } = useDashboard();

  useEffect(() => {
    actions.fetchAllData();
  }, []);

  // data.results now contains jobs data with archetype information
  // Existing components continue to work without changes
  return (
    <div>
      {data.results.map(job => (
        <div key={job.id}>
          <h3>{job.assessment_name}</h3>
          <p>Status: {job.status}</p>
          {job.archetype && <p>Archetype: {job.archetype}</p>}
        </div>
      ))}
    </div>
  );
}
```

## Migration Benefits

1. **Data Accuracy**: Dashboard sekarang menampilkan data jobs yang sebenarnya dicari user
2. **Archetype Integration**: Archetype information diambil langsung dari results endpoint
3. **Backward Compatibility**: Komponen existing tetap berfungsi tanpa perubahan
4. **Performance**: Data archetype hanya diambil untuk jobs yang completed
5. **Error Handling**: Graceful handling jika archetype gagal diambil

## Notes

- Jobs dengan status 'processing', 'pending', atau 'failed' tidak akan memiliki archetype
- Field `id` diset ke `result_id` untuk completed jobs, atau `job_id` untuk jobs lainnya
- Debug logging tersedia di console untuk monitoring data transformation
- Delete job functionality belum diimplementasi (placeholder tersedia)
