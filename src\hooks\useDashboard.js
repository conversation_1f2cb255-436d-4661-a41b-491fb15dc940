import { useState, useCallback } from 'react';
import apiService from '../services/apiService';

/**
 * Comprehensive dashboard hook for managing dashboard data
 */
export const useDashboard = () => {
  const [data, setData] = useState({
    stats: null,
    jobs: [],
    tokenBalance: null,
    pagination: {
      page: 1,
      limit: 10,
      total: 0,
      totalPages: 0
    }
  });

  const [loading, setLoading] = useState({
    stats: false,
    jobs: false,
    tokenBalance: false,
    initial: true
  });

  const [error, setError] = useState({
    stats: '',
    jobs: '',
    tokenBalance: '',
    general: ''
  });

  // Clear specific error
  const clearError = useCallback((type) => {
    setError(prev => ({ ...prev, [type]: '' }));
  }, []);

  // Fetch user statistics
  const fetchStats = useCallback(async () => {
    setLoading(prev => ({ ...prev, stats: true }));
    setError(prev => ({ ...prev, stats: '' }));

    try {
      const response = await apiService.getStats();
      setData(prev => ({ ...prev, stats: response.data }));
    } catch (err) {
      const errorMessage = err.response?.data?.message || err.message || 'Failed to fetch statistics';
      setError(prev => ({ ...prev, stats: errorMessage }));
      console.error('Error fetching stats:', err);
    } finally {
      setLoading(prev => ({ ...prev, stats: false }));
    }
  }, []);

  // Fetch user jobs with pagination and archetype data
  const fetchJobs = useCallback(async (page = 1, limit = 10) => {
    setLoading(prev => ({ ...prev, jobs: true }));
    setError(prev => ({ ...prev, jobs: '' }));

    try {
      const response = await apiService.getJobs({
        page,
        limit,
        sort: 'created_at',
        order: 'DESC'
      });

      if (response.success && response.data) {
        const jobs = response.data.jobs || [];

        // Untuk setiap job yang completed dan memiliki result_id, ambil archetype
        const jobsWithArchetype = await Promise.all(
          jobs.map(async (job) => {
            if (job.status === 'completed' && job.result_id) {
              try {
                const resultResponse = await apiService.getResultById(job.result_id);
                if (resultResponse.success && resultResponse.data) {
                  return {
                    ...job,
                    // Add id field for backward compatibility with existing components
                    id: job.result_id || job.job_id,
                    archetype: resultResponse.data.persona_profile?.archetype || null,
                    persona_profile: resultResponse.data.persona_profile || null
                  };
                }
              } catch (err) {
                console.warn(`Failed to fetch archetype for job ${job.job_id}:`, err);
              }
            }
            return {
              ...job,
              // Add id field for backward compatibility with existing components
              id: job.result_id || job.job_id,
              archetype: null,
              persona_profile: null
            };
          })
        );

        // Debug logging untuk melihat struktur data yang dikembalikan
        console.log('Dashboard Jobs:', {
          total_jobs: jobsWithArchetype.length,
          jobs_summary: jobsWithArchetype.map(j => ({
            job_id: j.job_id,
            status: j.status,
            assessment_name: j.assessment_name,
            archetype: j.archetype || 'none',
            has_result_id: !!j.result_id
          }))
        });

        setData(prev => ({
          ...prev,
          jobs: jobsWithArchetype,
          pagination: {
            page: response.data.page || page,
            limit: response.data.limit || limit,
            total: response.data.total || 0,
            totalPages: Math.ceil((response.data.total || 0) / (response.data.limit || limit))
          }
        }));
      }
    } catch (err) {
      const errorMessage = err.response?.data?.message || err.message || 'Failed to fetch jobs';
      setError(prev => ({ ...prev, jobs: errorMessage }));
      console.error('Error fetching jobs:', err);
    } finally {
      setLoading(prev => ({ ...prev, jobs: false }));
    }
  }, []);

  // Fetch token balance
  const fetchTokenBalance = useCallback(async () => {
    setLoading(prev => ({ ...prev, tokenBalance: true }));
    setError(prev => ({ ...prev, tokenBalance: '' }));

    try {
      const response = await apiService.getTokenBalance();
      setData(prev => ({ ...prev, tokenBalance: response.data }));
    } catch (err) {
      const errorMessage = err.response?.data?.message || err.message || 'Failed to fetch token balance';
      setError(prev => ({ ...prev, tokenBalance: errorMessage }));
      console.error('Error fetching token balance:', err);
    } finally {
      setLoading(prev => ({ ...prev, tokenBalance: false }));
    }
  }, []);

  // Fetch all dashboard data
  const fetchAllData = useCallback(async (page = 1, limit = 10) => {
    setLoading(prev => ({ ...prev, initial: true }));
    setError({
      stats: '',
      jobs: '',
      tokenBalance: '',
      general: ''
    });

    try {
      // Fetch all data in parallel
      await Promise.all([
        fetchStats(),
        fetchJobs(page, limit),
        fetchTokenBalance()
      ]);
    } catch (err) {
      setError(prev => ({
        ...prev,
        general: 'Failed to load dashboard data. Please try again.'
      }));
      console.error('Error fetching dashboard data:', err);
    } finally {
      setLoading(prev => ({ ...prev, initial: false }));
    }
  }, [fetchStats, fetchJobs, fetchTokenBalance]);

  // Delete a job (if needed - this might not be applicable for jobs)
  const deleteJob = useCallback(async (jobId) => {
    try {
      // Note: There might not be a delete job endpoint,
      // this is just for consistency with the original structure
      // You may need to implement this based on your API
      console.warn('Delete job functionality not implemented yet');
      return false;
    } catch (err) {
      console.error('Error deleting job:', err);
      throw err;
    }
  }, []);

  // Refresh all data
  const refreshData = useCallback(() => {
    fetchAllData(data.pagination.page, data.pagination.limit);
  }, [fetchAllData, data.pagination.page, data.pagination.limit]);

  // Change page
  const changePage = useCallback((newPage) => {
    fetchJobs(newPage, data.pagination.limit);
  }, [fetchJobs, data.pagination.limit]);

  return {
    data: {
      ...data,
      // Backward compatibility: alias jobs as results for existing components
      results: data.jobs
    },
    loading: {
      ...loading,
      // Backward compatibility: alias jobs loading as results loading
      results: loading.jobs
    },
    error: {
      ...error,
      // Backward compatibility: alias jobs error as results error
      results: error.jobs
    },
    actions: {
      fetchStats,
      fetchJobs,
      fetchTokenBalance,
      fetchAllData,
      deleteJob,
      refreshData,
      changePage,
      clearError,
      // Backward compatibility: alias fetchJobs as fetchResults
      fetchResults: fetchJobs,
      // Backward compatibility: alias deleteJob as deleteResult
      deleteResult: deleteJob
    },
    // Computed values
    isLoading: loading.initial || loading.stats || loading.jobs || loading.tokenBalance,
    hasError: !!(error.stats || error.jobs || error.tokenBalance || error.general),
    isEmpty: !data.stats && !data.jobs.length && !data.tokenBalance
  };
};

export default useDashboard;
