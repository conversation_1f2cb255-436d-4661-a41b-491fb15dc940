import { renderHook, act } from '@testing-library/react';
import { useDashboard } from './useDashboard';
import apiService from '../services/apiService';

// Mock apiService
jest.mock('../services/apiService');

describe('useDashboard Hook', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('should initialize with correct default state', () => {
    const { result } = renderHook(() => useDashboard());

    expect(result.current.data.jobs).toEqual([]);
    expect(result.current.data.results).toEqual([]); // Backward compatibility
    expect(result.current.loading.initial).toBe(true);
    expect(result.current.loading.jobs).toBe(false);
    expect(result.current.loading.results).toBe(false); // Backward compatibility
  });

  test('should fetch jobs and transform data correctly', async () => {
    const mockJobsResponse = {
      success: true,
      data: {
        jobs: [
          {
            job_id: 'job_123',
            status: 'completed',
            assessment_name: 'Test Assessment',
            created_at: '2024-01-15T10:30:00.000Z',
            result_id: 'result_456'
          },
          {
            job_id: 'job_789',
            status: 'processing',
            assessment_name: 'Test Assessment 2',
            created_at: '2024-01-14T09:15:00.000Z',
            result_id: null
          }
        ],
        total: 2
      }
    };

    const mockResultResponse = {
      success: true,
      data: {
        persona_profile: {
          archetype: 'The Innovator'
        }
      }
    };

    apiService.getJobs.mockResolvedValue(mockJobsResponse);
    apiService.getResultById.mockResolvedValue(mockResultResponse);

    const { result } = renderHook(() => useDashboard());

    await act(async () => {
      await result.current.actions.fetchJobs();
    });

    expect(result.current.data.jobs).toHaveLength(2);
    expect(result.current.data.results).toHaveLength(2); // Backward compatibility
    
    // Check first job (completed with archetype)
    expect(result.current.data.jobs[0]).toMatchObject({
      job_id: 'job_123',
      status: 'completed',
      assessment_name: 'Test Assessment',
      id: 'result_456',
      archetype: 'The Innovator',
      persona_profile: {
        archetype: 'The Innovator'
      }
    });

    // Check second job (processing without archetype)
    expect(result.current.data.jobs[1]).toMatchObject({
      job_id: 'job_789',
      status: 'processing',
      assessment_name: 'Test Assessment 2',
      id: 'job_789',
      archetype: null,
      persona_profile: null
    });
  });

  test('should handle API errors gracefully', async () => {
    const mockError = new Error('API Error');
    apiService.getJobs.mockRejectedValue(mockError);

    const { result } = renderHook(() => useDashboard());

    await act(async () => {
      await result.current.actions.fetchJobs();
    });

    expect(result.current.error.jobs).toBe('Failed to fetch jobs');
    expect(result.current.error.results).toBe('Failed to fetch jobs'); // Backward compatibility
    expect(result.current.hasError).toBe(true);
  });

  test('should provide backward compatibility aliases', () => {
    const { result } = renderHook(() => useDashboard());

    // Check that fetchResults is an alias for fetchJobs
    expect(result.current.actions.fetchResults).toBe(result.current.actions.fetchJobs);
    
    // Check that deleteResult is an alias for deleteJob
    expect(result.current.actions.deleteResult).toBe(result.current.actions.deleteJob);
  });
});
